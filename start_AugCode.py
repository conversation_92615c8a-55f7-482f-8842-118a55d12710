#!/usr/bin/env python3
"""
AI蛋白/抗体论文获取器 - 快速启动脚本
一键启动和配置整个系统
"""

import os
import sys
import subprocess
import yaml
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    
    try:
        # 检查是否有requirements文件
        if Path("requirements_AugCode.txt").exists():
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements_AugCode.txt"
            ])
            print("✅ 依赖包安装完成")
            return True
        else:
            print("❌ 找不到requirements_AugCode.txt文件")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def check_config_file():
    """检查配置文件"""
    config_file = "config_protein_ai_AugCode.yaml"
    
    if not Path(config_file).exists():
        print(f"❌ 找不到配置文件: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 配置文件检查通过")
        return True
    except Exception as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'output', 'data']
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🧬 AI蛋白/抗体论文获取器")
    print("="*50)
    print("1. 立即运行一次 (测试)")
    print("2. 启动定时调度器")
    print("3. 只获取ArXiv论文")
    print("4. 只获取BioRxiv论文")
    print("5. 配置邮件通知")
    print("6. 查看系统状态")
    print("7. 查看使用说明")
    print("0. 退出")
    print("="*50)

def run_once():
    """立即运行一次"""
    print("🚀 立即运行论文获取任务...")
    try:
        from scheduler_AugCode import PaperScheduler
        scheduler = PaperScheduler()
        scheduler.run_once()
        print("✅ 任务执行完成")
    except Exception as e:
        print(f"❌ 任务执行失败: {e}")

def start_scheduler():
    """启动定时调度器"""
    print("⏰ 启动定时调度器...")
    print("提示: 按 Ctrl+C 可以停止调度器")
    
    try:
        from scheduler_AugCode import PaperScheduler
        scheduler = PaperScheduler()
        scheduler.run_scheduler()
    except KeyboardInterrupt:
        print("\n✅ 调度器已停止")
    except Exception as e:
        print(f"❌ 调度器启动失败: {e}")

def run_arxiv_only():
    """只运行ArXiv获取"""
    print("📚 获取ArXiv论文...")
    try:
        from protein_paper_fetcher_AugCode import ProteinPaperFetcher
        fetcher = ProteinPaperFetcher()
        fetcher.run()
        print("✅ ArXiv论文获取完成")
    except Exception as e:
        print(f"❌ ArXiv论文获取失败: {e}")

def run_biorxiv_only():
    """只运行BioRxiv获取"""
    print("🧪 获取BioRxiv论文...")
    try:
        from biorxiv_fetcher_AugCode import BioRxivFetcher
        import yaml
        
        # 加载配置
        with open("config_protein_ai_AugCode.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        fetcher = BioRxivFetcher()
        papers = fetcher.fetch_all_platforms(config['keywords'], 1)
        
        if papers:
            print(f"✅ BioRxiv论文获取完成，找到 {len(papers)} 篇相关论文")
        else:
            print("✅ BioRxiv论文获取完成，但没有找到相关论文")
    except Exception as e:
        print(f"❌ BioRxiv论文获取失败: {e}")

def configure_email():
    """配置邮件通知"""
    print("📧 配置邮件通知...")
    
    config_file = "config_protein_ai_AugCode.yaml"
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("请输入邮件配置信息:")
        smtp_server = input("SMTP服务器 (如 smtp.gmail.com): ").strip()
        smtp_port = input("SMTP端口 (如 587): ").strip()
        username = input("发送邮箱: ").strip()
        password = input("邮箱密码/应用密码: ").strip()
        recipients = input("接收邮箱 (多个用逗号分隔): ").strip()
        
        if all([smtp_server, smtp_port, username, password, recipients]):
            config['notifications']['email'] = {
                'enabled': True,
                'smtp_server': smtp_server,
                'smtp_port': int(smtp_port),
                'username': username,
                'password': password,
                'recipients': [email.strip() for email in recipients.split(',')]
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            print("✅ 邮件配置已保存")
            
            # 测试邮件
            test = input("是否发送测试邮件? (y/n): ").strip().lower()
            if test == 'y':
                try:
                    from scheduler_AugCode import PaperScheduler
                    scheduler = PaperScheduler()
                    scheduler.send_notification(1, 1)
                    print("✅ 测试邮件发送成功")
                except Exception as e:
                    print(f"❌ 测试邮件发送失败: {e}")
        else:
            print("❌ 配置信息不完整")
    
    except Exception as e:
        print(f"❌ 邮件配置失败: {e}")

def show_status():
    """显示系统状态"""
    print("📊 系统状态:")
    print("-" * 30)
    
    # 检查文件
    files = [
        "config_protein_ai_AugCode.yaml",
        "protein_paper_fetcher_AugCode.py",
        "biorxiv_fetcher_AugCode.py",
        "scheduler_AugCode.py"
    ]
    
    for file in files:
        status = "✅" if Path(file).exists() else "❌"
        print(f"{status} {file}")
    
    # 检查数据库
    db_file = "protein_papers.db"
    if Path(db_file).exists():
        import sqlite3
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM papers")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"✅ 数据库: {count} 篇论文")
        except:
            print("❌ 数据库: 无法读取")
    else:
        print("⚪ 数据库: 尚未创建")
    
    # 检查最近的输出文件
    from datetime import datetime
    today = datetime.now().strftime('%Y-%m-%d')
    
    output_files = [
        f"daily_protein_ai_papers_{today}.md",
        f"biorxiv_papers_{today}.md",
        f"papers_{today}.json"
    ]
    
    print("\n📄 今日输出文件:")
    for file in output_files:
        status = "✅" if Path(file).exists() else "⚪"
        print(f"{status} {file}")

def show_help():
    """显示使用说明"""
    help_file = "使用说明_AugCode.md"
    
    if Path(help_file).exists():
        print(f"📖 请查看详细使用说明: {help_file}")
        
        # 显示快速开始部分
        try:
            with open(help_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取快速开始部分
            start_idx = content.find("## 快速开始")
            if start_idx != -1:
                end_idx = content.find("## ", start_idx + 1)
                if end_idx == -1:
                    end_idx = start_idx + 1000
                
                quick_start = content[start_idx:end_idx]
                print("\n" + quick_start)
        except:
            pass
    else:
        print("❌ 找不到使用说明文件")

def main():
    """主函数"""
    print("🧬 AI蛋白/抗体论文获取器 - 启动检查")
    print("-" * 50)
    
    # 系统检查
    if not check_python_version():
        return
    
    if not check_config_file():
        print("请确保config_protein_ai_AugCode.yaml文件存在且格式正确")
        return
    
    create_directories()
    
    # 询问是否安装依赖
    install = input("是否安装/更新依赖包? (y/n): ").strip().lower()
    if install == 'y':
        if not install_dependencies():
            return
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-7): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                run_once()
            elif choice == '2':
                start_scheduler()
            elif choice == '3':
                run_arxiv_only()
            elif choice == '4':
                run_biorxiv_only()
            elif choice == '5':
                configure_email()
            elif choice == '6':
                show_status()
            elif choice == '7':
                show_help()
            else:
                print("❌ 无效选择，请重新输入")
            
            if choice != '0':
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
