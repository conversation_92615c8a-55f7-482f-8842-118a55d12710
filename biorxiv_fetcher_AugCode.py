#!/usr/bin/env python3
"""
BioRxiv论文获取器
扩展支持BioRxiv、ChemRxiv、MedRxiv等平台的论文获取
"""

import requests
import json
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from dataclasses import dataclass
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)

@dataclass
class BioRxivPaper:
    """BioRxiv论文数据结构"""
    id: str
    title: str
    authors: List[str]
    abstract: str
    categories: List[str]
    published: str
    doi: str
    pdf_url: str
    abs_url: str
    server: str  # biorxiv, chemrxiv, medrxiv
    relevance_score: float = 0.0
    matched_keywords: List[str] = None
    
    def __post_init__(self):
        if self.matched_keywords is None:
            self.matched_keywords = []

class BioRxivFetcher:
    """BioRxiv等平台的论文获取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 平台配置
        self.platforms = {
            'biorxiv': {
                'base_url': 'https://www.biorxiv.org',
                'api_url': 'https://api.biorxiv.org/details/biorxiv',
                'categories': [
                    'bioinformatics', 'synthetic-biology', 'systems-biology',
                    'molecular-biology', 'biochemistry', 'cell-biology'
                ]
            },
            'medrxiv': {
                'base_url': 'https://www.medrxiv.org',
                'api_url': 'https://api.medrxiv.org/details/medrxiv',
                'categories': [
                    'immunology', 'infectious-diseases', 'pharmacology-toxicology'
                ]
            }
        }
    
    def get_biorxiv_papers_by_date(self, server: str = 'biorxiv', 
                                   start_date: str = None, 
                                   end_date: str = None) -> List[BioRxivPaper]:
        """
        通过BioRxiv API获取指定日期范围的论文
        
        Args:
            server: 'biorxiv' 或 'medrxiv'
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        if not start_date:
            start_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        papers = []
        
        try:
            api_url = self.platforms[server]['api_url']
            url = f"{api_url}/{start_date}/{end_date}"
            
            logger.info(f"获取{server}论文: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'collection' in data:
                for item in data['collection']:
                    paper = self._parse_biorxiv_paper(item, server)
                    if paper:
                        papers.append(paper)
            
            logger.info(f"从{server}获取到 {len(papers)} 篇论文")
            
        except Exception as e:
            logger.error(f"获取{server}论文失败: {e}")
        
        return papers
    
    def _parse_biorxiv_paper(self, item: Dict, server: str) -> Optional[BioRxivPaper]:
        """解析BioRxiv API返回的论文数据"""
        try:
            # 处理作者信息
            authors = []
            if 'authors' in item:
                authors = [author.get('name', '') for author in item['authors']]
            
            # 处理分类信息
            categories = []
            if 'category' in item:
                categories = [item['category']]
            
            paper = BioRxivPaper(
                id=item.get('doi', '').replace('10.1101/', ''),
                title=item.get('title', ''),
                authors=authors,
                abstract=item.get('abstract', ''),
                categories=categories,
                published=item.get('date', ''),
                doi=item.get('doi', ''),
                pdf_url=f"https://www.{server}.org/content/10.1101/{item.get('doi', '').replace('10.1101/', '')}.full.pdf",
                abs_url=f"https://www.{server}.org/content/10.1101/{item.get('doi', '').replace('10.1101/', '')}",
                server=server
            )
            
            return paper
            
        except Exception as e:
            logger.error(f"解析{server}论文数据失败: {e}")
            return None
    
    def scrape_biorxiv_category(self, server: str, category: str, 
                               days_back: int = 1) -> List[BioRxivPaper]:
        """
        通过网页爬取获取特定分类的论文
        当API不可用时的备用方案
        """
        papers = []
        
        try:
            base_url = self.platforms[server]['base_url']
            url = f"{base_url}/collection/{category}"
            
            logger.info(f"爬取{server}分类页面: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找论文条目
            paper_items = soup.find_all('div', class_='highwire-cite-metadata')
            
            for item in paper_items[:20]:  # 限制数量避免过载
                paper = self._parse_scraped_paper(item, server)
                if paper:
                    papers.append(paper)
                    
            logger.info(f"从{server}/{category}爬取到 {len(papers)} 篇论文")
            
        except Exception as e:
            logger.error(f"爬取{server}/{category}失败: {e}")
        
        return papers
    
    def _parse_scraped_paper(self, item, server: str) -> Optional[BioRxivPaper]:
        """解析爬取的论文HTML元素"""
        try:
            # 提取标题
            title_elem = item.find('span', class_='highwire-cite-title')
            title = title_elem.get_text().strip() if title_elem else ''
            
            # 提取作者
            authors_elem = item.find('span', class_='highwire-cite-authors')
            authors = []
            if authors_elem:
                author_links = authors_elem.find_all('a')
                authors = [link.get_text().strip() for link in author_links]
            
            # 提取DOI
            doi_elem = item.find('span', class_='highwire-cite-metadata-doi')
            doi = doi_elem.get_text().replace('doi:', '').strip() if doi_elem else ''
            
            # 提取发布日期
            date_elem = item.find('span', class_='highwire-cite-metadata-date')
            published = date_elem.get_text().strip() if date_elem else ''
            
            if not title or not doi:
                return None
            
            paper_id = doi.replace('10.1101/', '')
            
            paper = BioRxivPaper(
                id=paper_id,
                title=title,
                authors=authors,
                abstract='',  # 需要额外请求获取
                categories=[],
                published=published,
                doi=doi,
                pdf_url=f"https://www.{server}.org/content/10.1101/{paper_id}.full.pdf",
                abs_url=f"https://www.{server}.org/content/10.1101/{paper_id}",
                server=server
            )
            
            return paper
            
        except Exception as e:
            logger.error(f"解析爬取的论文数据失败: {e}")
            return None
    
    def get_paper_abstract(self, paper: BioRxivPaper) -> str:
        """获取论文摘要（如果之前没有获取到）"""
        if paper.abstract:
            return paper.abstract
        
        try:
            response = self.session.get(paper.abs_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找摘要部分
            abstract_elem = soup.find('div', {'id': 'abstract-1'}) or \
                           soup.find('div', class_='section abstract') or \
                           soup.find('p', {'id': 'p-2'})
            
            if abstract_elem:
                abstract = abstract_elem.get_text().strip()
                # 清理文本
                abstract = re.sub(r'\s+', ' ', abstract)
                paper.abstract = abstract
                return abstract
            
        except Exception as e:
            logger.error(f"获取论文摘要失败 {paper.id}: {e}")
        
        return ""
    
    def calculate_relevance_score(self, paper: BioRxivPaper, 
                                keywords_config: Dict) -> float:
        """计算论文相关性得分"""
        score = 0.0
        matched_keywords = []
        
        # 获取摘要（如果还没有）
        if not paper.abstract:
            self.get_paper_abstract(paper)
        
        # 合并标题和摘要进行匹配
        text = (paper.title + " " + paper.abstract).lower()
        
        for topic, config in keywords_config.items():
            filters = config['filters']
            weight = config.get('weight', 1.0)
            topic_matched = False
            
            for keyword in filters:
                if keyword.lower() in text:
                    if not topic_matched:
                        score += weight
                        matched_keywords.append(topic)
                        topic_matched = True
                    break
        
        paper.matched_keywords = matched_keywords
        paper.relevance_score = score
        
        return score
    
    def fetch_all_platforms(self, keywords_config: Dict, 
                           days_back: int = 1) -> List[BioRxivPaper]:
        """从所有配置的平台获取论文"""
        all_papers = []
        
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        for server in ['biorxiv', 'medrxiv']:
            try:
                # 方法1: 使用API
                papers = self.get_biorxiv_papers_by_date(server, start_date, end_date)
                
                # 如果API获取的论文较少，尝试爬取
                if len(papers) < 5:
                    logger.info(f"{server} API获取论文较少，尝试爬取补充")
                    categories = self.platforms[server]['categories']
                    for category in categories[:2]:  # 限制分类数量
                        scraped_papers = self.scrape_biorxiv_category(server, category, days_back)
                        papers.extend(scraped_papers)
                        time.sleep(2)  # 避免请求过快
                
                # 计算相关性得分
                relevant_papers = []
                for paper in papers:
                    score = self.calculate_relevance_score(paper, keywords_config)
                    if score > 0:
                        relevant_papers.append(paper)
                
                all_papers.extend(relevant_papers)
                logger.info(f"从{server}获取到 {len(relevant_papers)} 篇相关论文")
                
                time.sleep(3)  # 平台间请求间隔
                
            except Exception as e:
                logger.error(f"获取{server}论文失败: {e}")
                continue
        
        # 按相关性得分排序
        all_papers.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return all_papers

def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 示例关键词配置
    keywords_config = {
        "蛋白质设计": {
            "filters": ["protein design", "protein engineering", "de novo protein"],
            "weight": 1.0
        },
        "抗体工程": {
            "filters": ["antibody design", "antibody engineering", "therapeutic antibody"],
            "weight": 1.2
        }
    }
    
    fetcher = BioRxivFetcher()
    papers = fetcher.fetch_all_platforms(keywords_config, days_back=2)
    
    print(f"获取到 {len(papers)} 篇相关论文:")
    for paper in papers[:5]:  # 显示前5篇
        print(f"- {paper.title} (得分: {paper.relevance_score:.2f})")
        print(f"  来源: {paper.server}, DOI: {paper.doi}")
        print()

if __name__ == "__main__":
    main()
