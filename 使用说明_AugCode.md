# AI蛋白/抗体论文获取器使用说明

## 项目概述

这是一个专门针对AI蛋白/抗体领域的论文获取工具，基于对三个优秀开源项目（cv-arxiv-daily、zotero-arxiv-daily、daily-arXiv-ai-enhanced）的深入分析和最佳实践提取而设计。

## 核心特性

✅ **本地部署** - 无需GitHub Actions，完全本地运行  
✅ **智能筛选** - 基于关键词和相关性评分的精准筛选  
✅ **多数据源** - 支持ArXiv RSS和搜索API，可扩展到BioRxiv  
✅ **专业领域** - 专门针对AI蛋白/抗体研究优化  
✅ **多种输出** - 支持Markdown、JSON、HTML和数据库存储  
✅ **可配置** - 灵活的YAML配置文件  

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境（推荐）
python -m venv protein_papers_env
source protein_papers_env/bin/activate  # Linux/Mac
# 或
protein_papers_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements_AugCode.txt
```

### 2. 配置设置

编辑 `config_protein_ai_AugCode.yaml` 文件：

```yaml
# 基本设置
base_settings:
  max_results_per_category: 50  # 调整获取论文数量
  days_back: 1                  # 获取几天前的论文
  output_format: ["markdown", "json"]  # 选择输出格式
```

### 3. 运行程序

```bash
python protein_paper_fetcher_AugCode.py
```

## 详细配置说明

### ArXiv分类配置

针对AI蛋白/抗体领域，我们预配置了以下相关分类：

```yaml
arxiv_categories:
  - "q-bio.BM"     # 生物分子
  - "q-bio.QM"     # 定量方法  
  - "cs.LG"        # 机器学习
  - "cs.AI"        # 人工智能
  - "stat.ML"      # 统计机器学习
```

### 关键词配置

系统预设了8个主要研究方向的关键词：

1. **蛋白质生成** - protein generation, de novo protein等
2. **蛋白质优化** - protein engineering, directed evolution等
3. **功能预测** - protein function prediction, enzyme function等
4. **属性预测** - stability prediction, solubility prediction等
5. **结构预测** - AlphaFold, protein folding等
6. **抗体设计** - antibody design, therapeutic antibody等
7. **抗体预测** - epitope prediction, binding affinity等
8. **深度学习方法** - transformer protein, GNN protein等

### 权重系统

每个关键词类别都有权重设置：

```yaml
"抗体设计":
  weight: 1.2  # 更高权重，重点关注
"功能预测":
  weight: 0.9  # 较低权重，次要关注
```

## 输出格式

### 1. Markdown报告
- 文件名：`daily_protein_ai_papers_YYYY-MM-DD.md`
- 按主题分组显示论文
- 包含标题、作者、摘要、链接等信息

### 2. SQLite数据库
- 文件名：`protein_papers.db`
- 持久化存储所有论文数据
- 支持历史查询和分析

### 3. JSON数据
- 文件名：`papers_YYYY-MM-DD.json`
- 结构化数据，便于程序处理

## 扩展到其他平台

### BioRxiv支持

虽然BioRxiv没有传统API，但可以通过以下方式获取数据：

1. **Amazon S3批量访问**（推荐）
```python
# 在protein_paper_fetcher_AugCode.py中添加
def fetch_biorxiv_papers(self):
    # 通过S3获取BioRxiv数据
    # 需要额外的AWS配置
    pass
```

2. **网页爬取**
```python
# 使用requests + BeautifulSoup
def scrape_biorxiv_category(self, category):
    url = f"https://www.biorxiv.org/collection/{category}"
    # 爬取逻辑
```

### ChemRxiv和MedRxiv

在配置文件中启用：

```yaml
other_platforms:
  chemrxiv:
    enabled: true
    categories: ["chemical-biology"]
  medrxiv:
    enabled: true
    categories: ["immunology"]
```

## 定时运行设置

### 方法1: 使用cron（Linux/Mac）

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天上午9点运行）
0 9 * * * cd /path/to/project && python protein_paper_fetcher_AugCode.py
```

### 方法2: 使用Windows任务计划程序

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为"每天"
4. 设置操作为运行Python脚本

### 方法3: 使用Python schedule库

```python
import schedule
import time

def job():
    fetcher = ProteinPaperFetcher()
    fetcher.run()

schedule.every().day.at("09:00").do(job)

while True:
    schedule.run_pending()
    time.sleep(60)
```

## 高级功能

### AI摘要生成

配置OpenAI API：

```yaml
ai_summary:
  provider: "openai"
  model: "gpt-3.5-turbo"
  language: "chinese"
```

### 邮件通知

配置SMTP设置：

```yaml
notifications:
  email:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
```

### Web界面（可选）

创建简单的Flask应用查看结果：

```python
from flask import Flask, render_template
import sqlite3

app = Flask(__name__)

@app.route('/')
def index():
    # 从数据库读取论文数据
    # 渲染HTML模板
    pass
```

## 常见问题

### Q1: 获取不到论文怎么办？
- 检查网络连接
- 确认ArXiv分类是否正确
- 调整关键词匹配策略

### Q2: 如何提高筛选精度？
- 调整关键词权重
- 添加更精确的关键词
- 使用排除关键词过滤无关论文

### Q3: 如何处理API限制？
- 增加请求间隔时间
- 使用批量请求
- 实现重试机制

### Q4: 数据库文件过大怎么办？
- 定期清理旧数据
- 使用数据压缩
- 考虑使用PostgreSQL等更强大的数据库

## 贡献和反馈

这个工具是基于开源项目的最佳实践设计的，欢迎：

- 报告bug和问题
- 提出功能建议
- 贡献代码改进
- 分享使用经验

## 许可证

本项目采用Apache 2.0许可证，与参考的开源项目保持一致。
