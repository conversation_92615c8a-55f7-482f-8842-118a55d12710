#!/usr/bin/env python3
"""
论文获取任务调度器
实现本地自动化运行，替代GitHub Actions
"""

import schedule
import time
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import yaml

# 导入我们的模块
from protein_paper_fetcher_AugCode import ProteinPaperFetcher
from biorxiv_fetcher_AugCode import BioRxivFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PaperScheduler:
    """论文获取调度器"""
    
    def __init__(self, config_path: str = "config_protein_ai_AugCode.yaml"):
        self.config_path = config_path
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            logger.info("配置文件加载成功")
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    def run_arxiv_task(self):
        """运行ArXiv论文获取任务"""
        logger.info("开始执行ArXiv论文获取任务")
        
        try:
            fetcher = ProteinPaperFetcher(self.config_path)
            fetcher.run()
            logger.info("ArXiv任务执行成功")
            return True
        except Exception as e:
            logger.error(f"ArXiv任务执行失败: {e}")
            return False
    
    def run_biorxiv_task(self):
        """运行BioRxiv论文获取任务"""
        logger.info("开始执行BioRxiv论文获取任务")
        
        try:
            fetcher = BioRxivFetcher()
            keywords_config = self.config['keywords']
            days_back = self.config['base_settings']['days_back']
            
            papers = fetcher.fetch_all_platforms(keywords_config, days_back)
            
            if papers:
                # 保存BioRxiv论文到单独的文件
                self.save_biorxiv_results(papers)
                logger.info(f"BioRxiv任务执行成功，获取到 {len(papers)} 篇论文")
            else:
                logger.info("BioRxiv任务执行成功，但没有找到相关论文")
            
            return True
        except Exception as e:
            logger.error(f"BioRxiv任务执行失败: {e}")
            return False
    
    def save_biorxiv_results(self, papers):
        """保存BioRxiv结果"""
        date_str = datetime.now().strftime('%Y-%m-%d')
        
        # 保存为Markdown
        filename = f"biorxiv_papers_{date_str}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# BioRxiv/MedRxiv AI蛋白/抗体论文 - {date_str}\n\n")
            f.write(f"共找到 {len(papers)} 篇相关论文\n\n")
            
            for paper in papers:
                f.write(f"## {paper.title}\n")
                f.write(f"**作者**: {', '.join(paper.authors[:3])}{'...' if len(paper.authors) > 3 else ''}\n\n")
                f.write(f"**发布时间**: {paper.published}\n\n")
                f.write(f"**相关性得分**: {paper.relevance_score:.2f}\n\n")
                f.write(f"**匹配关键词**: {', '.join(paper.matched_keywords)}\n\n")
                f.write(f"**来源**: {paper.server.upper()}\n\n")
                f.write(f"**DOI**: {paper.doi}\n\n")
                f.write(f"**链接**: [PDF]({paper.pdf_url}) | [Abstract]({paper.abs_url})\n\n")
                if paper.abstract:
                    f.write(f"**摘要**: {paper.abstract[:300]}...\n\n")
                f.write("---\n\n")
        
        logger.info(f"BioRxiv结果已保存到: {filename}")
    
    def run_daily_task(self):
        """运行每日任务"""
        logger.info("=" * 50)
        logger.info(f"开始执行每日论文获取任务 - {datetime.now()}")
        
        success_count = 0
        total_tasks = 2
        
        # 任务1: ArXiv
        if self.run_arxiv_task():
            success_count += 1
        
        # 任务2: BioRxiv (如果配置启用)
        if self.config.get('other_platforms', {}).get('biorxiv', {}).get('enabled', False):
            if self.run_biorxiv_task():
                success_count += 1
        else:
            total_tasks = 1  # 只有ArXiv任务
        
        # 发送通知
        if self.config.get('notifications', {}).get('email', {}).get('enabled', False):
            self.send_notification(success_count, total_tasks)
        
        logger.info(f"每日任务完成: {success_count}/{total_tasks} 个任务成功")
        logger.info("=" * 50)
    
    def send_notification(self, success_count: int, total_tasks: int):
        """发送邮件通知"""
        try:
            email_config = self.config['notifications']['email']
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = email_config['username']
            msg['To'] = ', '.join(email_config['recipients'])
            msg['Subject'] = f"AI蛋白/抗体论文获取报告 - {datetime.now().strftime('%Y-%m-%d')}"
            
            # 邮件内容
            body = f"""
            每日论文获取任务完成报告：
            
            执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            任务状态: {success_count}/{total_tasks} 个任务成功
            
            {'✅ 所有任务执行成功' if success_count == total_tasks else '⚠️ 部分任务执行失败'}
            
            请查看生成的报告文件获取详细信息。
            
            ---
            AI蛋白/抗体论文获取器
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info("邮件通知发送成功")
            
        except Exception as e:
            logger.error(f"邮件通知发送失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        run_time = self.config.get('schedule', {}).get('run_time', '09:00')
        
        # 每日定时任务
        schedule.every().day.at(run_time).do(self.run_daily_task)
        
        # 可选: 每周总结任务
        # schedule.every().monday.at("10:00").do(self.run_weekly_summary)
        
        logger.info(f"定时任务已设置: 每天 {run_time} 执行")
    
    def run_scheduler(self):
        """运行调度器"""
        logger.info("论文获取调度器启动")
        logger.info("按 Ctrl+C 停止调度器")
        
        self.setup_schedule()
        
        # 显示下次运行时间
        next_run = schedule.next_run()
        if next_run:
            logger.info(f"下次运行时间: {next_run}")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("调度器已停止")
    
    def run_once(self):
        """立即运行一次（用于测试）"""
        logger.info("立即执行一次论文获取任务")
        self.run_daily_task()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='AI蛋白/抗体论文获取调度器')
    parser.add_argument('--config', default='config_protein_ai_AugCode.yaml',
                       help='配置文件路径')
    parser.add_argument('--once', action='store_true',
                       help='立即运行一次，不启动调度器')
    parser.add_argument('--test-email', action='store_true',
                       help='测试邮件通知功能')
    
    args = parser.parse_args()
    
    try:
        scheduler = PaperScheduler(args.config)
        
        if args.test_email:
            scheduler.send_notification(2, 2)
        elif args.once:
            scheduler.run_once()
        else:
            scheduler.run_scheduler()
            
    except Exception as e:
        logger.error(f"调度器运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
