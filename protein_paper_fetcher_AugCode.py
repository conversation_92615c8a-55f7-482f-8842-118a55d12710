#!/usr/bin/env python3
"""
AI蛋白/抗体论文获取器
基于三个参考项目的最佳实践，专门针对AI蛋白/抗体领域设计的本地化解决方案
"""

import os
import re
import json
import yaml
import arxiv
import sqlite3
import logging
import requests
import feedparser
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('protein_papers.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class Paper:
    """论文数据结构"""
    id: str
    title: str
    authors: List[str]
    abstract: str
    categories: List[str]
    published: str
    updated: str
    pdf_url: str
    abs_url: str
    relevance_score: float = 0.0
    matched_keywords: List[str] = None
    ai_summary: str = ""
    source: str = "arxiv"
    
    def __post_init__(self):
        if self.matched_keywords is None:
            self.matched_keywords = []

class ProteinPaperFetcher:
    """AI蛋白/抗体论文获取器主类"""
    
    def __init__(self, config_path: str = "config_protein_ai_AugCode.yaml"):
        """初始化"""
        self.config = self.load_config(config_path)
        self.arxiv_client = arxiv.Client(
            page_size=100,
            delay_seconds=3,
            num_retries=3
        )
        self.setup_database()
        
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    def setup_database(self):
        """设置SQLite数据库"""
        if not self.config['base_settings']['local_storage']:
            return
            
        self.db_path = self.config['output']['database']['filename']
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建论文表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS papers (
                id TEXT PRIMARY KEY,
                title TEXT,
                authors TEXT,
                abstract TEXT,
                categories TEXT,
                published TEXT,
                updated TEXT,
                pdf_url TEXT,
                abs_url TEXT,
                relevance_score REAL,
                matched_keywords TEXT,
                ai_summary TEXT,
                source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
    
    def get_arxiv_papers_by_rss(self, categories: List[str], days_back: int = 1) -> List[str]:
        """通过RSS获取ArXiv论文ID列表"""
        paper_ids = []
        
        for category in categories:
            try:
                rss_url = f"https://rss.arxiv.org/atom/{category}"
                logger.info(f"获取RSS: {rss_url}")
                
                feed = feedparser.parse(rss_url)
                if hasattr(feed, 'bozo') and feed.bozo:
                    logger.warning(f"RSS解析警告: {category}")
                
                # 获取指定天数内的新论文
                cutoff_date = datetime.now() - timedelta(days=days_back)
                
                for entry in feed.entries:
                    if hasattr(entry, 'arxiv_announce_type') and entry.arxiv_announce_type == 'new':
                        # 检查发布时间
                        pub_date = datetime(*entry.published_parsed[:6])
                        if pub_date >= cutoff_date:
                            paper_id = entry.id.split('/')[-1]
                            paper_ids.append(paper_id)
                
                logger.info(f"从 {category} 获取到 {len([p for p in paper_ids if p])} 篇新论文")
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                logger.error(f"获取RSS失败 {category}: {e}")
                continue
        
        return list(set(paper_ids))  # 去重
    
    def get_arxiv_papers_by_search(self, keywords: Dict, max_results: int = 50) -> List[str]:
        """通过关键词搜索获取ArXiv论文ID"""
        paper_ids = []
        
        for topic, config in keywords.items():
            filters = config['filters']
            query_parts = []
            
            # 构建查询字符串
            for filter_term in filters:
                if ' ' in filter_term:
                    query_parts.append(f'"{filter_term}"')
                else:
                    query_parts.append(filter_term)
            
            query = f"({' OR '.join(query_parts)})"
            
            try:
                logger.info(f"搜索关键词: {topic}")
                search = arxiv.Search(
                    query=query,
                    max_results=max_results,
                    sort_by=arxiv.SortCriterion.SubmittedDate
                )
                
                for result in self.arxiv_client.results(search):
                    paper_ids.append(result.get_short_id())
                
                logger.info(f"关键词 '{topic}' 搜索到 {len(paper_ids)} 篇论文")
                time.sleep(2)  # 避免请求过快
                
            except Exception as e:
                logger.error(f"关键词搜索失败 {topic}: {e}")
                continue
        
        return list(set(paper_ids))  # 去重
    
    def fetch_paper_details(self, paper_ids: List[str]) -> List[Paper]:
        """批量获取论文详细信息"""
        papers = []
        batch_size = 50
        
        logger.info(f"开始获取 {len(paper_ids)} 篇论文的详细信息")
        
        for i in range(0, len(paper_ids), batch_size):
            batch_ids = paper_ids[i:i+batch_size]
            
            try:
                search = arxiv.Search(id_list=batch_ids)
                
                for result in self.arxiv_client.results(search):
                    paper = Paper(
                        id=result.get_short_id(),
                        title=result.title,
                        authors=[str(author) for author in result.authors],
                        abstract=result.summary.replace('\n', ' '),
                        categories=result.categories,
                        published=result.published.strftime('%Y-%m-%d'),
                        updated=result.updated.strftime('%Y-%m-%d'),
                        pdf_url=result.pdf_url,
                        abs_url=result.entry_id
                    )
                    papers.append(paper)
                
                logger.info(f"批次 {i//batch_size + 1}: 获取到 {len(batch_ids)} 篇论文")
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"获取论文详情失败 (批次 {i//batch_size + 1}): {e}")
                continue
        
        logger.info(f"总共获取到 {len(papers)} 篇论文的详细信息")
        return papers
    
    def calculate_relevance_score(self, paper: Paper) -> Tuple[float, List[str]]:
        """计算论文相关性得分"""
        score = 0.0
        matched_keywords = []
        
        # 合并标题和摘要进行匹配
        text = (paper.title + " " + paper.abstract).lower()
        
        keywords_config = self.config['keywords']
        
        for topic, config in keywords_config.items():
            filters = config['filters']
            weight = config.get('weight', 1.0)
            topic_matched = False
            
            for keyword in filters:
                if keyword.lower() in text:
                    if not topic_matched:  # 每个主题只计算一次
                        score += weight
                        matched_keywords.append(topic)
                        topic_matched = True
                    break
        
        # 检查排除关键词
        exclude_keywords = self.config.get('exclude_keywords', [])
        for exclude_word in exclude_keywords:
            if exclude_word.lower() in text:
                score -= 0.5  # 降低得分
        
        return max(0.0, score), matched_keywords
    
    def filter_papers(self, papers: List[Paper]) -> List[Paper]:
        """筛选和评分论文"""
        filtered_papers = []
        
        for paper in papers:
            score, matched_keywords = self.calculate_relevance_score(paper)
            
            if score > 0:  # 只保留有相关性的论文
                paper.relevance_score = score
                paper.matched_keywords = matched_keywords
                filtered_papers.append(paper)
        
        # 按相关性得分排序
        filtered_papers.sort(key=lambda x: x.relevance_score, reverse=True)
        
        logger.info(f"筛选后保留 {len(filtered_papers)} 篇相关论文")
        return filtered_papers
    
    def save_papers(self, papers: List[Paper]):
        """保存论文到数据库"""
        if not self.config['base_settings']['local_storage']:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for paper in papers:
            cursor.execute('''
                INSERT OR REPLACE INTO papers 
                (id, title, authors, abstract, categories, published, updated, 
                 pdf_url, abs_url, relevance_score, matched_keywords, ai_summary, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                paper.id,
                paper.title,
                json.dumps(paper.authors),
                paper.abstract,
                json.dumps(paper.categories),
                paper.published,
                paper.updated,
                paper.pdf_url,
                paper.abs_url,
                paper.relevance_score,
                json.dumps(paper.matched_keywords),
                paper.ai_summary,
                paper.source
            ))
        
        conn.commit()
        conn.close()
        logger.info(f"已保存 {len(papers)} 篇论文到数据库")
    
    def generate_markdown_report(self, papers: List[Paper], date_str: str):
        """生成Markdown报告"""
        filename = self.config['output']['markdown']['filename'].format(date=date_str)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# AI蛋白/抗体论文日报 - {date_str}\n\n")
            f.write(f"共找到 {len(papers)} 篇相关论文\n\n")
            
            # 按主题分组
            topics = {}
            for paper in papers:
                for keyword in paper.matched_keywords:
                    if keyword not in topics:
                        topics[keyword] = []
                    topics[keyword].append(paper)
            
            for topic, topic_papers in topics.items():
                f.write(f"## {topic} ({len(topic_papers)} 篇)\n\n")
                
                for paper in topic_papers:
                    f.write(f"### {paper.title}\n")
                    f.write(f"**作者**: {', '.join(paper.authors[:3])}{'...' if len(paper.authors) > 3 else ''}\n\n")
                    f.write(f"**发布时间**: {paper.published}\n\n")
                    f.write(f"**相关性得分**: {paper.relevance_score:.2f}\n\n")
                    f.write(f"**分类**: {', '.join(paper.categories)}\n\n")
                    f.write(f"**链接**: [PDF]({paper.pdf_url}) | [Abstract]({paper.abs_url})\n\n")
                    f.write(f"**摘要**: {paper.abstract[:300]}...\n\n")
                    if paper.ai_summary:
                        f.write(f"**AI摘要**: {paper.ai_summary}\n\n")
                    f.write("---\n\n")
        
        logger.info(f"Markdown报告已生成: {filename}")
    
    def run(self):
        """主运行函数"""
        logger.info("开始运行AI蛋白/抗体论文获取器")
        
        # 获取配置
        categories = self.config['arxiv_categories']
        keywords = self.config['keywords']
        max_results = self.config['base_settings']['max_results_per_category']
        days_back = self.config['base_settings']['days_back']
        
        # 方法1: 通过RSS获取新论文
        logger.info("方法1: 通过RSS获取新论文")
        rss_paper_ids = self.get_arxiv_papers_by_rss(categories, days_back)
        
        # 方法2: 通过关键词搜索
        logger.info("方法2: 通过关键词搜索")
        search_paper_ids = self.get_arxiv_papers_by_search(keywords, max_results)
        
        # 合并并去重
        all_paper_ids = list(set(rss_paper_ids + search_paper_ids))
        logger.info(f"总共获取到 {len(all_paper_ids)} 个唯一论文ID")
        
        if not all_paper_ids:
            logger.warning("没有找到任何论文")
            return
        
        # 获取论文详细信息
        papers = self.fetch_paper_details(all_paper_ids)
        
        # 筛选和评分
        filtered_papers = self.filter_papers(papers)
        
        if not filtered_papers:
            logger.warning("没有找到相关论文")
            return
        
        # 保存到数据库
        self.save_papers(filtered_papers)
        
        # 生成报告
        date_str = datetime.now().strftime('%Y-%m-%d')
        if 'markdown' in self.config['base_settings']['output_format']:
            self.generate_markdown_report(filtered_papers, date_str)
        
        logger.info(f"任务完成! 共处理 {len(filtered_papers)} 篇相关论文")

def main():
    """主函数"""
    try:
        fetcher = ProteinPaperFetcher()
        fetcher.run()
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        raise

if __name__ == "__main__":
    main()
