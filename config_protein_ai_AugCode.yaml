# AI蛋白/抗体论文获取配置文件
# 基于三个参考项目的最佳实践设计

# 基本设置
base_settings:
  max_results_per_category: 50  # 每个分类最大获取论文数
  days_back: 1  # 获取几天前的论文，1表示昨天
  output_format: ["markdown", "json", "html"]  # 输出格式
  enable_ai_summary: true  # 是否启用AI摘要
  local_storage: true  # 是否本地存储数据

# ArXiv分类配置
arxiv_categories:
  # 生物相关
  - "q-bio.BM"  # Biomolecules 生物分子
  - "q-bio.QM"  # Quantitative Methods 定量方法
  - "q-bio.MN"  # Molecular Networks 分子网络
  
  # AI/ML相关
  - "cs.LG"     # Machine Learning 机器学习
  - "cs.AI"     # Artificial Intelligence 人工智能
  - "stat.ML"   # Machine Learning (Statistics) 统计机器学习
  
  # 计算生物学
  - "cs.CE"     # Computational Engineering 计算工程
  - "physics.bio-ph"  # Biological Physics 生物物理

# 关键词筛选配置
keywords:
  # 蛋白质从头生成
  "蛋白质生成":
    filters: 
      - "protein generation"
      - "protein design"
      - "de novo protein"
      - "protein synthesis"
      - "generative protein"
      - "protein creation"
    weight: 1.0
    
  # 蛋白质优化
  "蛋白质优化":
    filters:
      - "protein optimization"
      - "protein engineering"
      - "directed evolution"
      - "protein improvement"
      - "protein enhancement"
      - "protein modification"
    weight: 1.0
    
  # 功能预测
  "功能预测":
    filters:
      - "protein function prediction"
      - "functional annotation"
      - "enzyme function"
      - "protein activity prediction"
      - "GO annotation"
      - "functional genomics"
    weight: 0.9
    
  # 属性性质预测
  "属性预测":
    filters:
      - "protein property prediction"
      - "stability prediction"
      - "solubility prediction"
      - "thermostability"
      - "protein characteristics"
      - "physicochemical properties"
    weight: 0.9
    
  # 结构预测
  "结构预测":
    filters:
      - "protein structure prediction"
      - "AlphaFold"
      - "protein folding"
      - "structure modeling"
      - "3D structure"
      - "structural biology"
      - "fold recognition"
    weight: 1.0
    
  # 抗体设计
  "抗体设计":
    filters:
      - "antibody design"
      - "antibody engineering"
      - "therapeutic antibody"
      - "antibody optimization"
      - "monoclonal antibody"
      - "antibody development"
    weight: 1.2  # 更高权重，因为是重点关注领域
    
  # 抗体预测
  "抗体预测":
    filters:
      - "antibody prediction"
      - "epitope prediction"
      - "paratope prediction"
      - "antibody-antigen"
      - "binding affinity"
      - "immunogenicity prediction"
    weight: 1.2
    
  # 深度学习方法
  "深度学习方法":
    filters:
      - "deep learning protein"
      - "neural network protein"
      - "transformer protein"
      - "CNN protein"
      - "GNN protein"
      - "attention mechanism"
      - "protein language model"
    weight: 0.8
    
  # 生成模型
  "生成模型":
    filters:
      - "generative model"
      - "VAE protein"
      - "GAN protein"
      - "diffusion model"
      - "autoregressive"
      - "sequence generation"
    weight: 0.8

# 排除关键词（降低相关性）
exclude_keywords:
  - "review"  # 综述文章
  - "survey"  # 调研文章
  - "tutorial"  # 教程
  - "benchmark"  # 基准测试（可选排除）

# 其他平台配置
other_platforms:
  biorxiv:
    enabled: true
    categories: ["bioinformatics", "synthetic-biology", "systems-biology"]
    base_url: "https://www.biorxiv.org"
    
  chemrxiv:
    enabled: false  # 暂不启用
    categories: ["chemical-biology"]
    
  medrxiv:
    enabled: false  # 暂不启用

# AI摘要配置
ai_summary:
  provider: "openai"  # 或 "deepseek", "qwen"
  model: "gpt-3.5-turbo"
  language: "chinese"
  max_tokens: 200
  prompt_template: |
    请为以下论文生成一个简洁的中文摘要，重点关注：
    1. 研究的核心问题
    2. 使用的方法或技术
    3. 主要发现或贡献
    4. 在AI蛋白/抗体领域的意义
    
    论文标题：{title}
    论文摘要：{abstract}

# 输出配置
output:
  markdown:
    filename: "daily_protein_ai_papers_{date}.md"
    template: "markdown_template.md"
    
  json:
    filename: "papers_{date}.json"
    include_full_text: false
    
  html:
    filename: "papers_{date}.html"
    template: "html_template.html"
    
  database:
    type: "sqlite"
    filename: "protein_papers.db"
    
# 通知配置
notifications:
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    recipients: []
    
  webhook:
    enabled: false
    url: ""
    
# 调度配置
schedule:
  run_time: "09:00"  # 每天运行时间
  timezone: "Asia/Shanghai"
  
# 日志配置
logging:
  level: "INFO"
  filename: "protein_papers.log"
  max_size: "10MB"
  backup_count: 5
