# AI蛋白/抗体论文获取器依赖包
# 基于三个参考项目的最佳实践

# 核心依赖
arxiv>=1.4.0              # ArXiv API客户端
requests>=2.28.0          # HTTP请求库
pyyaml>=6.0               # YAML配置文件解析
feedparser>=6.0.10        # RSS feed解析

# 数据处理
sqlite3                   # 内置SQLite数据库
json                      # 内置JSON处理
datetime                  # 内置日期时间处理
pathlib                   # 内置路径处理
dataclasses               # 内置数据类

# 网页处理（用于扩展到其他平台）
beautifulsoup4>=4.11.0    # HTML解析
lxml>=4.9.0               # XML/HTML解析器
scrapy>=2.8.0             # 网页爬虫框架（可选）

# AI增强功能（可选）
openai>=1.0.0             # OpenAI API
sentence-transformers>=2.2.0  # 语义相似性计算
transformers>=4.21.0      # Hugging Face transformers

# 任务调度
schedule>=1.2.0           # 任务调度
apscheduler>=3.9.0        # 高级任务调度（可选）

# Web界面（可选）
flask>=2.3.0              # Web框架
jinja2>=3.1.0             # 模板引擎

# 邮件通知（可选）
smtplib                   # 内置SMTP客户端
email                     # 内置邮件处理

# 日志和监控
logging                   # 内置日志
tqdm>=4.64.0              # 进度条

# 数据科学（可选增强）
pandas>=1.5.0             # 数据分析
numpy>=1.21.0             # 数值计算
matplotlib>=3.5.0         # 数据可视化
seaborn>=0.11.0           # 统计可视化

# 文本处理
re                        # 内置正则表达式
nltk>=3.7                 # 自然语言处理（可选）
spacy>=3.4.0              # 高级NLP（可选）

# 配置管理
python-dotenv>=0.19.0     # 环境变量管理
configparser              # 内置配置解析

# 测试（开发用）
pytest>=7.0.0             # 测试框架
pytest-cov>=4.0.0         # 测试覆盖率
